package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseExecuteConfigDO;
import com.pugwoo.branch.database.entity.DatabaseExecuteResultDO;
import com.pugwoo.branch.database.enums.Constants;
import com.pugwoo.branch.database.enums.DatabaseExecuteStatusEnum;
import com.pugwoo.branch.database.service.DatabaseExecuteConfigService;
import com.pugwoo.branch.database.service.DatabaseExecuteResultService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
public class DatabaseExecuteConfigServiceImpl implements DatabaseExecuteConfigService {

    @Autowired
    private DBHelper dbHelper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private DatabaseExecuteResultService databaseExecuteResultService;

    @Override
    public DatabaseExecuteConfigDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DatabaseExecuteConfigDO.class, id);
    }

    @Override
    public PageData<DatabaseExecuteConfigDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseExecuteConfigDO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseExecuteConfigDO databaseExecuteConfigDO) {
        if(databaseExecuteConfigDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        boolean isInsert = databaseExecuteConfigDO.getId() == null;
        if (isInsert) {
            databaseExecuteConfigDO.setStatus(DatabaseExecuteStatusEnum.SAVED.getCode());
        }

        int rows = dbHelper.insertOrUpdate(databaseExecuteConfigDO);
        // 移除自动执行逻辑，改为手动执行

        return rows > 0 ? ResultBean.ok(databaseExecuteConfigDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DatabaseExecuteConfigDO databaseExecuteConfigDO = new DatabaseExecuteConfigDO();
        databaseExecuteConfigDO.setId(id);
        return dbHelper.delete(databaseExecuteConfigDO) > 0;
    }

    @Override
    public void startExecute(Long id) {
        if (id == null) {
            return;
        }

        DatabaseExecuteConfigDO config = dbHelper.getByKey(DatabaseExecuteConfigDO.class, id);
        if (config == null) {
            return;
        }

        // 只有已保存或失败状态的任务才能手动执行
        String status = config.getStatus();
        if (!DatabaseExecuteStatusEnum.SAVED.getCode().equals(status)
            && !DatabaseExecuteStatusEnum.FAILED.getCode().equals(status)) {
            log.warn("DatabaseExecuteConfig {} is not in SAVED or FAILED status, current status: {}", id, status);
            return;
        }

        // 设置为待执行状态并发送MQ消息
        config.setStatus(DatabaseExecuteStatusEnum.WAIT.getCode());
        dbHelper.update(config);
        redisHelper.send(Constants.DATABASE_EXECUTE_CONFIG_TASK_MQ, id.toString());
    }

    @Override
    @Synchronized(keyScript = "args[0]")
    public void execute(Long id) {
        DatabaseExecuteConfigDO config = dbHelper.getByKey(DatabaseExecuteConfigDO.class, id);
        if (config == null) {
            return;
        }

        if (!DatabaseExecuteStatusEnum.WAIT.getCode().equals(config.getStatus())) {
            log.warn("DatabaseExecuteConfig {} is not WAIT status, current status: {}", id, config.getStatus());
            return;
        }

        config.setStatus(DatabaseExecuteStatusEnum.RUNNING.getCode());
        dbHelper.update(config);

        // 开始执行
        if (isKeyInteger(config)) {
            try {
                long keyStart = Long.parseLong(config.getKeyStart());
                long keyEnd = Long.parseLong(config.getKeyEnd());
                long batchOffset = Long.parseLong(config.getBatchOffset());

                if (keyStart > keyEnd) {
                    config.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
                    config.setErrorMessage("开始主键值大于结束主键值");
                    config.setLastExecuteTime(LocalDateTime.now());
                    dbHelper.update(config);
                    return;
                }

                if (batchOffset <= 0) {
                    config.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
                    config.setErrorMessage("批次偏移量必须大于0");
                    config.setLastExecuteTime(LocalDateTime.now());
                    dbHelper.update(config);
                    return;
                }

                // 执行批次处理
                for (long currentKey = keyStart; currentKey <= keyEnd; currentKey += batchOffset) {
                    long batchEnd = Math.min(currentKey + batchOffset - 1, keyEnd);
                    String sql = config.getSql()
                            .replace(":keyStart", String.valueOf(currentKey))
                            .replace(":keyEnd", String.valueOf(batchEnd));

                    // 记录开始时间
                    long startTime = System.currentTimeMillis();

                    try {
                        // 执行SQL
                        log.info("执行SQL: {}", sql);
                        // 获取数据库连接信息
                        DatabaseDO databaseDO = databaseService.getById(config.getDatabaseId());

                        // 判断SQL类型
                        int affectedRows = 0;
                        String sqlBegin = getBegin10Lower(sql);
                        if (sqlBegin.startsWith("select") || sqlBegin.startsWith("with")) {
                            databaseService.executeQuery(databaseDO, config.getDatabaseName(), sql);
                        } else {
                            affectedRows = databaseService.executeModify(databaseDO, config.getDatabaseName(), sql);
                        }

                        // 计算执行时间
                        long endTime = System.currentTimeMillis();
                        int executionTime = (int) (endTime - startTime);

                        // 保存执行结果
                        DatabaseExecuteResultDO resultDO = new DatabaseExecuteResultDO();
                        resultDO.setName(config.getName());
                        resultDO.setTaskId(config.getId());
                        resultDO.setStatus(DatabaseExecuteStatusEnum.SUCCESS.getCode());
                        resultDO.setSql(sql);
                        resultDO.setAffectedRows(affectedRows);
                        resultDO.setCurrentKeyValue(String.valueOf(currentKey));
                        resultDO.setExecutionTime(executionTime);  // 设置执行时间
                        databaseExecuteResultService.insertOrUpdate(resultDO);

                        // 更新进度
                        config.setCurrentKeyValue(String.valueOf(batchEnd));
                        dbHelper.update(config);
                    } catch (Exception e) {
                        log.error("执行SQL出错: {}", sql, e);

                        // 计算执行时间（即使失败也要记录）
                        long endTime = System.currentTimeMillis();
                        int executionTime = (int) (endTime - startTime);

                        // 保存执行失败结果
                        DatabaseExecuteResultDO resultDO = new DatabaseExecuteResultDO();
                        resultDO.setName(config.getName());
                        resultDO.setTaskId(config.getId());
                        resultDO.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
                        resultDO.setSql(sql);
                        resultDO.setCurrentKeyValue(String.valueOf(currentKey));
                        resultDO.setErrorMessage(e.getMessage());
                        resultDO.setExecutionTime(executionTime);  // 设置执行时间
                        databaseExecuteResultService.insertOrUpdate(resultDO);

                        // 更新任务状态为失败
                        config.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
                        config.setErrorMessage("执行SQL出错: " + e.getMessage());
                        dbHelper.update(config);
                        return;
                    }
                }

                // 全部执行完成
                config.setStatus(DatabaseExecuteStatusEnum.SUCCESS.getCode());
                config.setLastExecuteTime(LocalDateTime.now());
                dbHelper.update(config);
            } catch (NumberFormatException e) {
                config.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
                config.setErrorMessage("解析整数主键失败: " + e.getMessage());
                config.setLastExecuteTime(LocalDateTime.now());
                dbHelper.update(config);
            }
        } else {
            config.setStatus(DatabaseExecuteStatusEnum.FAILED.getCode());
            config.setErrorMessage("不支持非整数形式的主键遍历");
            config.setLastExecuteTime(LocalDateTime.now());
            dbHelper.update(config);
        }
    }


    /**判断配置的主键遍历是否是整数*/
    private static boolean isKeyInteger(DatabaseExecuteConfigDO configDO) {
        if (configDO == null) {
            return false;
        }
        if (isInteger(configDO.getKeyStart()) && isInteger(configDO.getKeyEnd())
                && isInteger(configDO.getBatchOffset())) {
            return true;
        }
        return false;
    }

    private static boolean isInteger(String s) {
        if (s == null || s.isEmpty()) return false;
        return s.matches("-?\\d+");
    }

    private static String getBegin10Lower(String sql) {
        sql = removeComments(sql);
        String trimmed = sql.trim();
        int length = Math.min(trimmed.length(), 10);
        return trimmed.substring(0, length).toLowerCase();
    }

    private static String removeComments(String sql) {
        // 去除多行注释 /* */
        String noMultiLineComments = sql.replaceAll("/\\*.*?\\*/", "");
        // 去除单行注释 --
        String noComments = noMultiLineComments.replaceAll("--.*(?=\\n)", "");
        // 去除可能的Carriage Return (\r)
        return noComments.replaceAll("\\r", "");
    }
}

